//! Modal and Dialog components for Terminus UI React-like interface
//!
//! This module provides modal/dialog components that can be used with the rsx! macro
//! and follow the Shadcn UI Dialog API pattern.

use crossterm::event::{Event, KeyCode};
use ratatui::{
    Frame,
    layout::Rect,
    style::{Color, Style},
};
use terminus_ui_component_macro::component;
use terminus_ui_core::{
    Callback, Children, Element, FunctionalComponent, HasChildren, ModalProps, TrySetChildren,
    VirtualNode, WidgetType,
};
use terminus_ui_props_macro::Props;

/// Props for the Dialog component (main modal container)
///
/// This follows the Shadcn UI Dialog API pattern:
/// ```rust
/// <Dialog open={is_open} on_open_change={set_is_open}>
///   <DialogContent>
///     // modal content here
///   </DialogContent>
/// </Dialog>
/// ```
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct DialogProps {
    /// Whether the dialog is open
    pub open: bool,
    /// Callback called when the dialog should be opened or closed
    pub on_open_change: Callback<bool>,
    /// Child components to render inside the dialog
    #[children]
    pub children: Children,
}

/// Props for DialogContent component
#[derive(Props, Debug, Clone)]
pub struct DialogContentProps {
    /// Child components to render inside the dialog content
    #[children]
    pub children: Children,
    /// Optional title for the dialog (deprecated - use DialogTitle component instead)
    pub title: Option<String>,
    /// Whether the dialog can be closed with Escape key (default: true)
    pub closable: Option<bool>,
}

/// Props for DialogTitle component
#[derive(Props, Debug, Clone)]
pub struct DialogTitleProps {
    /// The title text to display
    #[children]
    pub children: Children,
}

/// Main Dialog component that provides the Shadcn-like API
///
/// This component manages the modal state and handles keyboard events.
/// It renders a modal overlay when open is true.
#[component(Dialog)]
pub fn dialog(props: DialogProps) -> Element {
    use terminus_ui_core::use_event;

    // Handle keyboard events
    if let Some(Event::Key(key)) = use_event() {
        if key.code == KeyCode::Esc {
            // Close the dialog when Escape is pressed
            if props.open {
                props.on_open_change.emit(false);
            }
        }
    }

    // Only render the modal if it's open
    if !props.open {
        return VirtualNode::Text(String::new());
    }

    // Create the modal widget with the children
    VirtualNode::widget(
        WidgetType::Modal,
        ModalProps {
            open: props.open,
            backdrop: Some(true),
            center: Some(true),
        },
        props.children.into_vec(),
    )
}

/// DialogContent component that renders the actual modal content
///
/// This component provides styling and structure for the modal content.
/// When used with DialogTitle as a sibling, it simply renders its children.
#[component(DialogContent)]
pub fn dialog_content(props: DialogContentProps) -> Element {
    use ratatui::layout::{Constraint, Direction};
    use terminus_ui_core::{LayoutProps, WidgetType};

    // Simple layout container for content
    let layout_props = LayoutProps {
        direction: Some(Direction::Vertical),
        constraints: Some(vec![Constraint::Min(0)]),
        margin: None, // No margin to avoid layout conflicts
    };

    VirtualNode::widget(WidgetType::Layout, layout_props, props.children.into_vec())
}

/// DialogTitle component that renders a styled title within the dialog
///
/// This component provides semantic structure for dialog titles, following Shadcn UI patterns.
/// It renders as styled text with minimal height and clear separation.
/// Supports both text content and Text components as children.
#[component(DialogTitle)]
pub fn dialog_title(props: DialogTitleProps) -> Element {
    use ratatui::layout::{Constraint, Direction};
    use terminus_ui_core::{LayoutProps, TextProps, WidgetType};

    // Check if we have Text components as children
    let children = props.children.into_vec();
    let has_text_components = children.iter().any(|child| {
        matches!(
            child,
            VirtualNode::Widget {
                widget_type: WidgetType::Text,
                ..
            }
        )
    });

    if has_text_components {
        // If we have Text components, create a layout to render them with title styling
        let styled_children: Vec<VirtualNode> = children
            .into_iter()
            .map(|child| match &child {
                VirtualNode::Widget {
                    widget_type: WidgetType::Text,
                    props,
                    ..
                } => {
                    // Apply title styling to Text components
                    if let Some(text_props) = props.get::<TextProps>() {
                        let styled_props = TextProps {
                            content: text_props.content.clone(),
                            style: Some(
                                Style::default()
                                    .fg(Color::Cyan)
                                    .add_modifier(ratatui::style::Modifier::BOLD)
                                    .add_modifier(ratatui::style::Modifier::UNDERLINED),
                            ),
                        };
                        VirtualNode::widget(WidgetType::Text, styled_props, vec![])
                    } else {
                        child
                    }
                }
                _ => child,
            })
            .collect();

        // Create a simple layout for the styled children
        let layout_props = LayoutProps {
            direction: Some(Direction::Horizontal),
            constraints: Some(vec![Constraint::Min(0)]),
            margin: None,
        };

        VirtualNode::widget(WidgetType::Layout, layout_props, styled_children)
    } else {
        // Fallback: extract text content from children (for backward compatibility)
        let title_text = children
            .into_iter()
            .filter_map(|child| match child {
                VirtualNode::Text(text) => Some(text),
                _ => None,
            })
            .collect::<Vec<_>>()
            .join(" ");

        // Create a styled text widget for the title
        let text_props = TextProps {
            content: title_text,
            style: Some(
                Style::default()
                    .fg(Color::Cyan)
                    .add_modifier(ratatui::style::Modifier::BOLD)
                    .add_modifier(ratatui::style::Modifier::UNDERLINED),
            ),
        };

        VirtualNode::widget(WidgetType::Text, text_props, vec![])
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dialog_props_creation() {
        let on_open_change = Callback::from(|_open: bool| {});
        let children = Children::new();

        let props = DialogProps {
            open: true,
            on_open_change,
            children,
        };

        assert!(props.open);
    }

    #[test]
    fn test_dialog_content_props_creation() {
        let children = Children::new();

        let props = DialogContentProps {
            children,
            title: Some("Test Dialog".to_string()),
            closable: Some(true),
        };

        assert_eq!(props.title, Some("Test Dialog".to_string()));
        assert_eq!(props.closable, Some(true));
    }
}
