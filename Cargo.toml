[workspace]
resolver = "2"
members = ["crates/*"]

[workspace.dependencies]
chrono = "0.4.40"
crossbeam = "0.8"
crossterm = "0.29"
lazy_static = "1.4.0"
once_cell = "1.21"
parking_lot = "0.12.4"
rand = "0.9.0"
ratatui = "0.29"
reqwest = { version = "0.12.21" }
serde = { version = "1.0" }
serde_json = "1.0"
crossbeam-channel = "0.5.8"
tokio = { version = "1.45.1", features = ["full"] }

better-panic = "0.3.0"
human-panic = "2.0.2"

tracing = "0.1"
tracing-futures = "0.2"
tracing-subscriber = { version = "0.3", default-features = false }
tracing-appender = "0.2"

convert_case = "0.8.0"
proc-macro2 = "1"
proc-macro2-diagnostics = "0.10"
quote = "1.0.40"
syn = "2.0.104"
trybuild = "1.0.105"

terminus_ui = { path = "crates/react" }
terminus_ui_component_macro = { path = "crates/component_macro" }
terminus_ui_core = { path = "crates/core" }
terminus_ui_core_macros = { path = "crates/core-macros" }
terminus_ui_rsx_macro = { path = "crates/rsx_macro" }
terminus_ui_props_macro = { path = "crates/props_macro" }
