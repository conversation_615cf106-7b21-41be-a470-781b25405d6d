use ratatui::{
    layout::{Constraint, Direction},
    style::Style,
    widgets::{Borders, ScrollbarOrientation},
};

/// Props for Block widget
#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct BlockProps {
    pub title: Option<String>,
    pub borders: Option<Borders>,
    pub border_style: Option<Style>,
}

/// Props for Text widget
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct TextProps {
    pub content: String,
    pub style: Option<Style>,
}

/// Props for Layout widget
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct LayoutProps {
    pub direction: Option<Direction>,
    pub constraints: Option<Vec<Constraint>>,
    pub margin: Option<u16>,
}

/// Props for Modal widget
#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult)]
pub struct ModalProps {
    pub open: bool,
    pub backdrop: Option<bool>,
    pub center: Option<bool>,
}

/// Props for Scrollbar widget
#[derive(<PERSON>bug, <PERSON><PERSON>)]
pub struct ScrollbarProps {
    pub orientation: ScrollbarOrientation,
    pub position: u16,
    pub content_length: u16,
    pub viewport_length: u16,
    pub style: Option<Style>,
    pub begin_symbol: Option<String>,
    pub end_symbol: Option<String>,
    pub thumb_symbol: Option<String>,
    pub track_symbol: Option<String>,
}

impl Default for ScrollbarProps {
    fn default() -> Self {
        Self {
            orientation: ScrollbarOrientation::VerticalRight,
            position: 0,
            content_length: 0,
            viewport_length: 0,
            style: None,
            begin_symbol: None,
            end_symbol: None,
            thumb_symbol: None,
            track_symbol: None,
        }
    }
}
