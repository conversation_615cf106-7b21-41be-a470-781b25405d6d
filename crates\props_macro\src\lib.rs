use proc_macro::TokenStream;
use quote::quote;
use syn::{Data, DeriveInput, Fields, Ident, parse_macro_input};

/// The #[derive(Props)] macro for creating type-safe component props
#[proc_macro_derive(Props, attributes(children))]
pub fn derive_props(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let output = generate_props_impl(&input);
    output.into()
}

/// Generate the Props trait implementation
fn generate_props_impl(input: &DeriveInput) -> proc_macro2::TokenStream {
    let struct_name = &input.ident;

    // Extract fields from the struct
    let fields = match &input.data {
        Data::Struct(data_struct) => match &data_struct.fields {
            Fields::Named(fields_named) => &fields_named.named,
            _ => {
                return quote! {
                    compile_error!("Props can only be derived for structs with named fields");
                };
            }
        },
        _ => {
            return quote! {
                compile_error!("Props can only be derived for structs");
            };
        }
    };

    // Generate field accessors
    let field_accessors = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;

        quote! {
            pub fn #field_name(&self) -> &#field_type {
                &self.#field_name
            }
        }
    });

    // Generate builder methods
    let builder_methods = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;
        let method_name = Ident::new(&format!("with_{}", field_name), field_name.span());

        quote! {
            pub fn #method_name(mut self, value: #field_type) -> Self {
                self.#field_name = value;
                self
            }
        }
    });

    // Check if any field has the #[children] attribute
    let children_field = fields
        .iter()
        .find(|field| has_children_attribute(&field.attrs));
    let has_children = children_field.is_some();

    // Generate Default implementation that handles optional fields intelligently
    let default_field_values = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;

        if is_option_type(field_type) {
            // Optional fields default to None
            quote! { #field_name: None }
        } else {
            // Required fields use Default::default() - this will fail if the type doesn't implement Default
            // This is intentional to catch cases where required fields don't have sensible defaults
            quote! { #field_name: Default::default() }
        }
    });

    // Generate HasChildren trait implementation if there's a children field
    let has_children_impl = if has_children {
        let children_field_name = children_field.unwrap().ident.as_ref().unwrap();
        quote! {
            impl HasChildren for #struct_name {
                fn children_field_name() -> &'static str {
                    stringify!(#children_field_name)
                }

                fn set_children(&mut self, children: Children) {
                    self.#children_field_name = children;
                }

                fn get_children(&self) -> &Children {
                    &self.#children_field_name
                }
            }
        }
    } else {
        quote! {}
    };

    let main_impl = quote! {
        // Automatically implement Default with intelligent field handling
        impl Default for #struct_name {
            fn default() -> Self {
                Self {
                    #(#default_field_values),*
                }
            }
        }

        impl #struct_name {
            #(#field_accessors)*

            #(#builder_methods)*

            /// Create an instance with only the provided fields, using defaults for others
            /// This helps avoid clippy warnings about needless struct updates
            pub fn with_fields() -> Self {
                Self::default()
            }
        }

        // Include HasChildren implementation if applicable
        #has_children_impl
    };

    // Generate TrySetChildren implementation
    let try_set_children_impl = if has_children {
        quote! {
            impl TrySetChildren for #struct_name {
                fn try_set_children(&mut self, children: Children) -> bool {
                    self.set_children(children);
                    true
                }
            }
        }
    } else {
        quote! {
            impl TrySetChildren for #struct_name {
                fn try_set_children(&mut self, _children: Children) -> bool {
                    false
                }
            }
        }
    };

    quote! {
        #main_impl
        #try_set_children_impl
    }
}

/// Helper function to check if a type is Option<T>
fn is_option_type(ty: &syn::Type) -> bool {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            return segment.ident == "Option";
        }
    }
    false
}

/// Helper function to check if a field has the #[children] attribute
fn has_children_attribute(attrs: &[syn::Attribute]) -> bool {
    attrs.iter().any(|attr| attr.path().is_ident("children"))
}

/// Helper function to check if a type is Children or Option<Children>
#[allow(dead_code)]
fn is_children_type(ty: &syn::Type) -> bool {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            if segment.ident == "Children" {
                return true;
            }
            // Check for Option<Children>
            if segment.ident == "Option" {
                if let syn::PathArguments::AngleBracketed(args) = &segment.arguments {
                    if let Some(syn::GenericArgument::Type(inner_type)) = args.args.first() {
                        return is_children_type(inner_type);
                    }
                }
            }
        }
    }
    false
}

/// Helper function to check if a type is Callback<T> or Callback<T, U>
fn _is_callback_type(ty: &syn::Type) -> bool {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            return segment.ident == "Callback";
        }
    }
    false
}

/// Helper function to extract callback input and output types
fn _extract_callback_types(ty: &syn::Type) -> Option<(syn::Type, Option<syn::Type>)> {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            if segment.ident == "Callback" {
                if let syn::PathArguments::AngleBracketed(args) = &segment.arguments {
                    let args: Vec<_> = args.args.iter().collect();
                    match args.len() {
                        1 => {
                            if let syn::GenericArgument::Type(input_type) = args[0] {
                                return Some((input_type.clone(), None));
                            }
                        }
                        2 => {
                            if let (
                                syn::GenericArgument::Type(input_type),
                                syn::GenericArgument::Type(output_type),
                            ) = (args[0], args[1])
                            {
                                return Some((input_type.clone(), Some(output_type.clone())));
                            }
                        }
                        _ => {}
                    }
                }
            }
        }
    }
    None
}
