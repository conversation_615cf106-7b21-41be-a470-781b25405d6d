use crate::{Children, Element, TypedProps};

/// Trait for components that can render themselves
pub trait Component {
    type Props;

    /// Render the component with the given props
    fn render(
        &self,
        props: &Self::Props,
        frame: Option<&mut ratatui::Frame>,
        area: Option<ratatui::layout::Rect>,
    ) -> Element;
}

/// Trait for props that can accept children
pub trait HasChildren {
    /// Get the name of the children field
    fn children_field_name() -> &'static str
    where
        Self: Sized;

    /// Set the children for this props instance
    fn set_children(&mut self, children: Children);

    /// Get the children from this props instance
    fn get_children(&self) -> &Children;
}

/// Trait for attempting to set children on props that may or may not support them
pub trait TrySetChildren {
    /// Try to set children if the props support it
    /// Returns true if children were set, false if not supported
    fn try_set_children(&mut self, children: Children) -> bool;
}

/// Base trait for all components - inspired by <PERSON><PERSON>'s BaseComponent
pub trait BaseComponent {
    /// The properties type for this component (like <PERSON><PERSON>'s Properties)
    type Properties;

    /// Get the component name
    fn component_name() -> &'static str;
}

/// Trait for functional components with enhanced registration
pub trait FunctionalComponent {
    /// The properties type for this component (like Yew's Properties)
    type Properties;

    /// Render function for the component
    fn render(
        props: &Self::Properties,
        frame: Option<&mut ratatui::Frame>,
        area: Option<ratatui::layout::Rect>,
    ) -> Element;

    /// Get the component name for registration
    fn component_name() -> &'static str
    where
        Self: Sized,
    {
        std::any::type_name::<Self>()
    }

    /// Create an element with typed props
    fn create_element(props: Self::Properties) -> Element
    where
        Self: Sized,
        Self::Properties: crate::CloneableProps + 'static,
    {
        crate::VirtualNode::component(
            Self::component_name(),
            props,
            vec![],
            |typed_props, frame, area| {
                if let Some(props) = typed_props.get::<Self::Properties>() {
                    Self::render(props, frame, area)
                } else {
                    crate::VirtualNode::Text("Error: Invalid props type".to_string())
                }
            },
        )
    }

    /// Create an element with typed props and children
    fn create_element_with_children(props: Self::Properties, children: Vec<Element>) -> Element
    where
        Self: Sized,
        Self::Properties: crate::CloneableProps + 'static,
    {
        crate::VirtualNode::component(
            Self::component_name(),
            props,
            children,
            |typed_props, frame, area| {
                if let Some(props) = typed_props.get::<Self::Properties>() {
                    Self::render(props, frame, area)
                } else {
                    use crate::IntoElement;
                    "Error: Invalid props type".into_element()
                }
            },
        )
    }
}

/// A boxed component that can be stored and called dynamically
/// Now unified to support optional frame and area parameters
pub type BoxedComponent = Box<
    dyn Fn(&TypedProps, Option<&mut ratatui::Frame>, Option<ratatui::layout::Rect>) -> Element
        + Send
        + Sync,
>;

/// Information about a registered component
#[derive(Debug, Clone)]
pub struct ComponentInfo {
    pub name: String,
    pub props_type_id: std::any::TypeId,
}

/// Enhanced component registry for storing component render functions with metadata
pub struct ComponentRegistry {
    components: std::collections::HashMap<String, BoxedComponent>,
    component_info: std::collections::HashMap<String, ComponentInfo>,
}

impl ComponentRegistry {
    pub fn new() -> Self {
        Self {
            components: std::collections::HashMap::new(),
            component_info: std::collections::HashMap::new(),
        }
    }

    /// Register a component with the given name and metadata
    pub fn register<F, P>(&mut self, name: impl Into<String>, render_fn: F)
    where
        F: Fn(&TypedProps, Option<&mut ratatui::Frame>, Option<ratatui::layout::Rect>) -> Element
            + Send
            + Sync
            + 'static,
        P: 'static,
    {
        let name = name.into();
        let info = ComponentInfo {
            name: name.clone(),
            props_type_id: std::any::TypeId::of::<P>(),
        };

        self.components.insert(name.clone(), Box::new(render_fn));
        self.component_info.insert(name, info);
    }

    /// Register a functional component
    pub fn register_functional_component<C>(&mut self)
    where
        C: FunctionalComponent + 'static,
        C::Properties: 'static,
    {
        let name = C::component_name().to_string();
        let info = ComponentInfo {
            name: name.clone(),
            props_type_id: std::any::TypeId::of::<C::Properties>(),
        };

        let render_fn = |typed_props: &TypedProps,
                         frame: Option<&mut ratatui::Frame>,
                         area: Option<ratatui::layout::Rect>|
         -> Element {
            if let Some(props) = typed_props.get::<C::Properties>() {
                // Pass through frame and area context to the component
                C::render(props, frame, area)
            } else {
                use crate::IntoElement;
                "Error: Invalid props type".into_element()
            }
        };

        self.components.insert(name.clone(), Box::new(render_fn));
        self.component_info.insert(name, info);
    }

    /// Get a component by name
    pub fn get(&self, name: &str) -> Option<&BoxedComponent> {
        self.components.get(name)
    }

    /// Get component info by name
    pub fn get_component_info(&self, name: &str) -> Option<&ComponentInfo> {
        self.component_info.get(name)
    }

    /// Check if a component is registered
    pub fn is_component(&self, name: &str) -> bool {
        self.components.contains_key(name)
    }

    /// Render a component with the given name and props
    pub fn render(
        &self,
        name: &str,
        props: &TypedProps,
        frame: Option<&mut ratatui::Frame>,
        area: Option<ratatui::layout::Rect>,
    ) -> Option<Element> {
        self.get(name)
            .map(|component| component(props, frame, area))
    }

    /// Get all registered component names
    pub fn component_names(&self) -> Vec<String> {
        self.components.keys().cloned().collect()
    }
}

impl Default for ComponentRegistry {
    fn default() -> Self {
        Self::new()
    }
}
