//! Layout component for Terminus UI
//!
//! This module provides a Layout component that wraps the built-in layout functionality
//! and can be used with the rsx! macro as a custom component.

use ratatui::Frame;
use ratatui::layout::{Constraint, Direction};
use ratatui::prelude::Rect;
use terminus_ui_component_macro::component;
use terminus_ui_core::Children;
use terminus_ui_core::FunctionalComponent;
use terminus_ui_core::HasChildren;
use terminus_ui_core::TrySetChildren;
use terminus_ui_core::{Element, VirtualNode, WidgetType};
use terminus_ui_props_macro::Props;

/// Props for the Layout component
#[derive(Props, Debug, Clone)]
pub struct LayoutProps {
    /// Layout direction (Vertical or Horizontal)
    pub direction: Direction,
    /// Layout constraints for child elements
    pub constraints: Vec<Constraint>,
    /// Margin around the layout
    pub margin: Option<u16>,
    /// Children elements to be laid out
    #[children]
    pub children: terminus_ui_core::Children,
}

/// Layout component that provides flexible layout capabilities
///
/// This component wraps the built-in layout functionality and provides
/// a React-like API for creating layouts with direction, constraints, and margins.
///
/// # Example
///
/// ```rust
/// use terminus_ui::prelude::*;
///
/// let element = rsx! {
///     <Layout
///         direction={Direction::Vertical}
///         constraints={vec![
///             Constraint::Length(3),
///             Constraint::Min(0),
///             Constraint::Length(3),
///         ]}
///     >
///         <Block title="Header" />
///         <Block title="Content" />
///         <Block title="Footer" />
///     </Layout>
/// };
/// ```
#[component(Layout)]
pub fn layout(props: LayoutProps, _frame: Option<&mut Frame>, _area: Option<Rect>) -> Element {
    // Convert our component props to the core LayoutProps
    let core_props = terminus_ui_core::LayoutProps {
        direction: Some(props.direction),
        constraints: Some(props.constraints),
        margin: props.margin,
    };

    // Create a VirtualNode using the core layout widget
    VirtualNode::widget(WidgetType::Layout, core_props, props.children.into_vec())
}
