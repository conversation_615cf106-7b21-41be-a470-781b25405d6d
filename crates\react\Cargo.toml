[package]
name = "terminus_ui"
version = "0.1.0"
edition = "2024"

[dependencies]
crossterm = { workspace = true }
once_cell = { workspace = true }
ratatui = { workspace = true }
terminus_ui_component_macro = { workspace = true }
terminus_ui_core = { workspace = true }
terminus_ui_core_macros = { workspace = true }
terminus_ui_props_macro = { workspace = true }
terminus_ui_rsx_macro = { workspace = true }
tokio = { version = "1.0", features = ["full"] }
