use proc_macro2::Span;
use syn::{Error, Type};

/// Custom error types for component macro with rich diagnostics
#[derive(Debug)]
pub enum ComponentMacroError {
    /// Function doesn't return Element type
    ReturnType { span: Span, found_type: String },
    /// Props type validation error
    PropsType { span: Span, props_type: String },
    /// Frame parameter has incorrect type
    FrameParameter { span: Span, found_type: String },
    /// Area parameter has incorrect type
    AreaParameter { span: Span, found_type: String },
    /// Invalid parameter order
    ParameterOrder { span: Span, message: String },
}

impl ComponentMacroError {
    /// Get validation suggestions for this error type
    /// This integrates with the validation system to provide actionable guidance
    pub fn get_suggestions(&self) -> Vec<String> {
        // Import the validation suggestions function
        use crate::validation::ComponentValidator;
        ComponentValidator::get_validation_suggestions(self)
    }

    /// Convert to syn::Error with rich diagnostic messages
    /// This method automatically includes suggestions from get_validation_suggestions()
    pub fn to_syn_error(&self) -> Error {
        // Get suggestions for this error type before consuming self
        let suggestions = self.get_suggestions();
        let suggestions_text = if suggestions.is_empty() {
            String::new()
        } else {
            format!(
                "\n\nSuggestions:\n{}",
                suggestions
                    .iter()
                    .enumerate()
                    .map(|(i, s)| format!("  {}. {}", i + 1, s))
                    .collect::<Vec<_>>()
                    .join("\n")
            )
        };

        match self {
            ComponentMacroError::ReturnType { span, found_type } => Error::new(
                *span,
                format!(
                    "Component function must return Element type. Found: {}\n\
                        \n\
                        Help: Change your function signature to:\n\
                        fn your_component(props: YourProps) -> Element {{\n\
                            rsx! {{ /* your JSX here */ }}\n\
                        }}{}",
                    found_type, suggestions_text
                ),
            ),
            ComponentMacroError::PropsType { span, props_type } => Error::new(
                *span,
                format!(
                    "Props parameter validation error. Found: {}\n\
                        \n\
                        Help: Ensure your props struct is properly defined:\n\
                        #[derive(Debug, Clone)]\n\
                        pub struct {} {{\n\
                            // your fields here\n\
                        }}{}",
                    props_type, props_type, suggestions_text
                ),
            ),
            ComponentMacroError::FrameParameter { span, found_type } => Error::new(
                *span,
                format!(
                    "Frame parameter must be &mut Frame. Found: {}\n\
                        \n\
                        Help: Change your parameter to:\n\
                        fn your_component(props: YourProps, frame: &mut Frame) -> Element {{\n\
                            // frame is available for advanced rendering\n\
                        }}{}",
                    found_type, suggestions_text
                ),
            ),
            ComponentMacroError::AreaParameter { span, found_type } => Error::new(
                *span,
                format!(
                    "Area parameter must be Rect. Found: {}\n\
                        \n\
                        Help: Change your parameter to:\n\
                        fn your_component(props: YourProps, area: Rect) -> Element {{\n\
                            // area contains the rendering dimensions\n\
                        }}{}",
                    found_type, suggestions_text
                ),
            ),
            ComponentMacroError::ParameterOrder { span, message } => Error::new(
                *span,
                format!(
                    "Invalid parameter order: {}\n\
                        \n\
                        Help: Component parameters should follow this order:\n\
                        1. Props parameter (required, first)\n\
                        2. Frame parameter (optional, &mut Frame)\n\
                        3. Area parameter (optional, Rect)\n\
                        \n\
                        Examples:\n\
                        fn basic_component(props: MyProps) -> Element\n\
                        fn frame_component(props: MyProps, frame: &mut Frame) -> Element\n\
                        fn area_component(props: MyProps, area: Rect) -> Element\n\
                        fn full_component(props: MyProps, frame: &mut Frame, area: Rect) -> Element{}",
                    message, suggestions_text
                ),
            ),
        }
    }

    /// Create an invalid return type error
    pub fn invalid_return_type(span: Span, found_type: impl Into<String>) -> Self {
        Self::ReturnType {
            span,
            found_type: found_type.into(),
        }
    }

    /// Create an invalid props type error
    pub fn invalid_props_type(span: Span, props_type: impl Into<String>) -> Self {
        Self::PropsType {
            span,
            props_type: props_type.into(),
        }
    }

    /// Create an invalid frame parameter error
    pub fn invalid_frame_parameter(span: Span, found_type: impl Into<String>) -> Self {
        Self::FrameParameter {
            span,
            found_type: found_type.into(),
        }
    }

    /// Create an invalid area parameter error
    pub fn invalid_area_parameter(span: Span, found_type: impl Into<String>) -> Self {
        Self::AreaParameter {
            span,
            found_type: found_type.into(),
        }
    }

    /// Create an invalid parameter order error
    pub fn invalid_parameter_order(span: Span, message: impl Into<String>) -> Self {
        Self::ParameterOrder {
            span,
            message: message.into(),
        }
    }
}

/// Result type for component macro operations
pub type ComponentResult<T> = Result<T, ComponentMacroError>;

/// Helper trait for converting types to error-friendly strings
pub trait TypeDisplay {
    fn display_type(&self) -> String;
}

impl TypeDisplay for Type {
    fn display_type(&self) -> String {
        quote::quote! { #self }.to_string()
    }
}
