//! ScrollArea Context System
//!
//! This module provides a professional context-based architecture for the ScrollArea component,
//! following React patterns for state management and component coordination.

use std::fmt;
use terminus_ui_core::{
    Callback, Context, StateSetter, create_context_with_default, use_context, use_context_provider,
    use_state,
};

/// Error types for ScrollArea operations
#[derive(Debug, Clone, PartialEq)]
pub enum ScrollAreaError {
    /// Context not found - ScrollArea components must be used within a ScrollArea provider
    ContextNotFound,
    /// Invalid scroll position - position exceeds content bounds
    InvalidScrollPosition {
        position: usize,
        max_position: usize,
    },
    /// Invalid viewport dimensions
    InvalidViewportDimensions { width: usize, height: usize },
    /// Invalid content dimensions
    InvalidContentDimensions { width: usize, height: usize },
}

impl fmt::Display for ScrollAreaError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ScrollAreaError::ContextNotFound => {
                write!(
                    f,
                    "ScrollArea context not found. Components must be used within a ScrollArea provider."
                )
            }
            ScrollAreaError::InvalidScrollPosition {
                position,
                max_position,
            } => {
                write!(
                    f,
                    "Invalid scroll position: {} exceeds maximum position: {}",
                    position, max_position
                )
            }
            ScrollAreaError::InvalidViewportDimensions { width, height } => {
                write!(f, "Invalid viewport dimensions: {}x{}", width, height)
            }
            ScrollAreaError::InvalidContentDimensions { width, height } => {
                write!(f, "Invalid content dimensions: {}x{}", width, height)
            }
        }
    }
}

impl std::error::Error for ScrollAreaError {}

/// Result type for ScrollArea operations
pub type ScrollAreaResult<T> = Result<T, ScrollAreaError>;

/// Scroll orientation for ScrollArea components
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ScrollOrientation {
    /// Vertical scrolling (up/down)
    Vertical,
    /// Horizontal scrolling (left/right)
    Horizontal,
    /// Both vertical and horizontal scrolling
    Both,
}

impl Default for ScrollOrientation {
    fn default() -> Self {
        Self::Vertical
    }
}

/// Position of the scrollbar relative to the content area
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ScrollbarPosition {
    /// Top edge (for horizontal scrollbars)
    Top,
    /// Bottom edge (for horizontal scrollbars)
    Bottom,
    /// Left edge (for vertical scrollbars)
    Left,
    /// Right edge (for vertical scrollbars)
    Right,
}

impl Default for ScrollbarPosition {
    fn default() -> Self {
        Self::Right
    }
}

/// Viewport dimensions and content information
#[derive(Debug, Default, Clone, PartialEq)]
pub struct ViewportInfo {
    /// Current viewport width
    pub width: usize,
    /// Current viewport height
    pub height: usize,
    /// Total content width
    pub content_width: usize,
    /// Total content height
    pub content_height: usize,
}

/// Scroll position state
#[derive(Debug, Default, Clone, PartialEq)]
pub struct ScrollPosition {
    /// Vertical scroll offset
    pub vertical: usize,
    /// Horizontal scroll offset
    pub horizontal: usize,
}

/// Scrollbar visibility state
#[derive(Debug, Default, Clone, PartialEq)]
pub struct ScrollbarVisibility {
    /// Whether vertical scrollbar should be visible
    pub vertical: bool,
    /// Whether horizontal scrollbar should be visible
    pub horizontal: bool,
}

/// Configuration for ScrollArea behavior
#[derive(Debug, Clone)]
pub struct ScrollAreaConfig {
    /// Scroll orientation support
    pub orientation: ScrollOrientation,
    /// Whether to show scrollbars
    pub show_scrollbars: bool,
    /// Scroll step size for keyboard navigation
    pub scroll_step: usize,
    /// Whether content should wrap
    pub wrap: bool,
    /// Position of vertical scrollbar
    pub vertical_scrollbar_position: ScrollbarPosition,
    /// Position of horizontal scrollbar
    pub horizontal_scrollbar_position: ScrollbarPosition,
}

impl Default for ScrollAreaConfig {
    fn default() -> Self {
        Self {
            orientation: ScrollOrientation::Vertical,
            show_scrollbars: true,
            scroll_step: 1,
            wrap: false,
            vertical_scrollbar_position: ScrollbarPosition::Right,
            horizontal_scrollbar_position: ScrollbarPosition::Bottom,
        }
    }
}

/// Main context for ScrollArea state management
#[derive(Debug, Default, Clone)]
pub struct ScrollAreaContext {
    /// Current scroll position
    pub scroll_position: ScrollPosition,
    /// Viewport and content dimensions
    pub viewport_info: ViewportInfo,
    /// Scrollbar visibility state
    pub scrollbar_visibility: ScrollbarVisibility,
    /// Configuration settings
    pub config: ScrollAreaConfig,
    /// Callback for scroll events
    pub on_scroll: Option<Callback<(usize, usize)>>,
}

impl ScrollAreaContext {
    /// Safely set vertical scroll position with bounds checking
    pub fn set_vertical_scroll_safe(&mut self, position: usize) -> ScrollAreaResult<()> {
        let max_scroll = self.max_vertical_scroll();
        if position > max_scroll {
            return Err(ScrollAreaError::InvalidScrollPosition {
                position,
                max_position: max_scroll,
            });
        }
        self.scroll_position.vertical = position;
        Ok(())
    }

    /// Safely set horizontal scroll position with bounds checking
    pub fn set_horizontal_scroll_safe(&mut self, position: usize) -> ScrollAreaResult<()> {
        let max_scroll = self.max_horizontal_scroll();
        if position > max_scroll {
            return Err(ScrollAreaError::InvalidScrollPosition {
                position,
                max_position: max_scroll,
            });
        }
        self.scroll_position.horizontal = position;
        Ok(())
    }

    /// Safely update viewport dimensions with validation
    pub fn update_viewport_safe(&mut self, width: usize, height: usize) -> ScrollAreaResult<()> {
        if width == 0 || height == 0 {
            return Err(ScrollAreaError::InvalidViewportDimensions { width, height });
        }
        self.viewport_info.width = width;
        self.viewport_info.height = height;
        self.scrollbar_visibility = self.calculate_scrollbar_visibility();
        Ok(())
    }

    /// Safely update content dimensions with validation
    pub fn update_content_safe(&mut self, width: usize, height: usize) -> ScrollAreaResult<()> {
        // Content dimensions can be 0 (empty content), so we don't validate against 0
        self.viewport_info.content_width = width;
        self.viewport_info.content_height = height;
        self.scrollbar_visibility = self.calculate_scrollbar_visibility();

        // Clamp scroll positions to new bounds
        let max_vertical = self.max_vertical_scroll();
        let max_horizontal = self.max_horizontal_scroll();

        if self.scroll_position.vertical > max_vertical {
            self.scroll_position.vertical = max_vertical;
        }
        if self.scroll_position.horizontal > max_horizontal {
            self.scroll_position.horizontal = max_horizontal;
        }

        Ok(())
    }

    /// Safely scroll by delta with bounds checking
    pub fn scroll_by_safe(
        &mut self,
        vertical_delta: isize,
        horizontal_delta: isize,
    ) -> ScrollAreaResult<()> {
        // Calculate new positions
        let new_vertical = if vertical_delta >= 0 {
            self.scroll_position
                .vertical
                .saturating_add(vertical_delta as usize)
        } else {
            self.scroll_position
                .vertical
                .saturating_sub((-vertical_delta) as usize)
        };

        let new_horizontal = if horizontal_delta >= 0 {
            self.scroll_position
                .horizontal
                .saturating_add(horizontal_delta as usize)
        } else {
            self.scroll_position
                .horizontal
                .saturating_sub((-horizontal_delta) as usize)
        };

        // Apply with bounds checking
        self.set_vertical_scroll_safe(new_vertical)?;
        self.set_horizontal_scroll_safe(new_horizontal)?;

        Ok(())
    }
}

/// Actions that can be performed on the ScrollArea context
#[derive(Debug, Clone)]
pub enum ScrollAction {
    /// Set vertical scroll position
    SetVerticalScroll(usize),
    /// Set horizontal scroll position
    SetHorizontalScroll(usize),
    /// Update viewport dimensions
    UpdateViewport(usize, usize), // width, height
    /// Update content dimensions
    UpdateContent(usize, usize), // width, height
    /// Scroll by delta (vertical, horizontal)
    ScrollBy(isize, isize),
    /// Scroll to specific position
    ScrollTo(usize, usize),
}

/// Context provider state that manages all ScrollArea state
#[derive(Debug, Clone)]
pub struct ScrollAreaContextState {
    /// Current context value
    pub context: ScrollAreaContext,
    /// State setter for scroll position
    pub set_scroll_position: StateSetter<ScrollPosition>,
    /// State setter for viewport info
    pub set_viewport_info: StateSetter<ViewportInfo>,
    /// State setter for scrollbar visibility
    pub set_scrollbar_visibility: StateSetter<ScrollbarVisibility>,
}

/// Create default context instance
pub fn default_scroll_area_context() -> Context<ScrollAreaContext> {
    create_context_with_default(ScrollAreaContext::default())
}

/// Hook to provide ScrollArea context to child components
/// Returns (context, state_setters) for updating the context
pub fn use_scroll_area_provider(
    config: ScrollAreaConfig,
) -> (ScrollAreaContext, ScrollAreaContextState) {
    // Initialize state hooks
    let (scroll_position, set_scroll_position) = use_state(ScrollPosition::default());
    let (viewport_info, set_viewport_info) = use_state(ViewportInfo::default());
    let (scrollbar_visibility, set_scrollbar_visibility) =
        use_state(ScrollbarVisibility::default());

    // Create context value with current state - this will update when state changes
    let context = ScrollAreaContext {
        scroll_position: scroll_position.get(),
        viewport_info: viewport_info.get(),
        scrollbar_visibility: scrollbar_visibility.get(),
        config: config.clone(),
        on_scroll: None, // Will be set by the main component
    };

    // Create state setters for updating context
    let state_setters = ScrollAreaContextState {
        context: context.clone(),
        set_scroll_position,
        set_viewport_info,
        set_scrollbar_visibility,
    };

    // Provide the updated context to child components - this ensures children get the latest state
    use_context_provider(|| context.clone());

    (context, state_setters)
}

/// Hook to consume ScrollArea context in child components
pub fn use_scroll_area_context() -> ScrollAreaContext {
    use_context::<ScrollAreaContext>()
}

/// Hook to consume ScrollArea context with default fallback
pub fn use_scroll_area_context_with_default() -> ScrollAreaContext {
    let default_context = default_scroll_area_context();
    terminus_ui_core::use_context_with_default(&default_context)
}

/// Utility functions for ScrollArea context calculations
impl ScrollAreaContext {
    /// Calculate maximum vertical scroll position
    pub fn max_vertical_scroll(&self) -> usize {
        self.viewport_info
            .content_height
            .saturating_sub(self.viewport_info.height)
    }

    /// Calculate maximum horizontal scroll position
    pub fn max_horizontal_scroll(&self) -> usize {
        self.viewport_info
            .content_width
            .saturating_sub(self.viewport_info.width)
    }

    /// Check if vertical scrolling is needed
    pub fn needs_vertical_scrolling(&self) -> bool {
        self.viewport_info.content_height > self.viewport_info.height
    }

    /// Check if horizontal scrolling is needed
    pub fn needs_horizontal_scrolling(&self) -> bool {
        self.viewport_info.content_width > self.viewport_info.width
    }

    /// Calculate vertical scroll ratio (0.0 to 1.0)
    pub fn vertical_scroll_ratio(&self) -> f64 {
        let max_scroll = self.max_vertical_scroll();
        if max_scroll == 0 {
            0.0
        } else {
            self.scroll_position.vertical as f64 / max_scroll as f64
        }
    }

    /// Calculate horizontal scroll ratio (0.0 to 1.0)
    pub fn horizontal_scroll_ratio(&self) -> f64 {
        let max_scroll = self.max_horizontal_scroll();
        if max_scroll == 0 {
            0.0
        } else {
            self.scroll_position.horizontal as f64 / max_scroll as f64
        }
    }

    /// Calculate vertical content ratio (visible content / total content)
    pub fn vertical_content_ratio(&self) -> f64 {
        if self.viewport_info.content_height == 0 {
            1.0
        } else {
            (self.viewport_info.height as f64 / self.viewport_info.content_height as f64).min(1.0)
        }
    }

    /// Calculate horizontal content ratio (visible content / total content)
    pub fn horizontal_content_ratio(&self) -> f64 {
        if self.viewport_info.content_width == 0 {
            1.0
        } else {
            (self.viewport_info.width as f64 / self.viewport_info.content_width as f64).min(1.0)
        }
    }

    /// Clamp scroll position to valid bounds
    pub fn clamp_scroll_position(&self, vertical: usize, horizontal: usize) -> ScrollPosition {
        ScrollPosition {
            vertical: vertical.min(self.max_vertical_scroll()),
            horizontal: horizontal.min(self.max_horizontal_scroll()),
        }
    }

    /// Update scrollbar visibility based on content and configuration
    pub fn calculate_scrollbar_visibility(&self) -> ScrollbarVisibility {
        ScrollbarVisibility {
            vertical: self.config.show_scrollbars
                && self.needs_vertical_scrolling()
                && (self.config.orientation == ScrollOrientation::Vertical
                    || self.config.orientation == ScrollOrientation::Both),
            horizontal: self.config.show_scrollbars
                && self.needs_horizontal_scrolling()
                && (self.config.orientation == ScrollOrientation::Horizontal
                    || self.config.orientation == ScrollOrientation::Both),
        }
    }
}

/// Context action dispatcher for handling scroll actions
pub struct ScrollAreaDispatcher {
    set_scroll_position: StateSetter<ScrollPosition>,
    set_viewport_info: StateSetter<ViewportInfo>,
    set_scrollbar_visibility: StateSetter<ScrollbarVisibility>,
    context: ScrollAreaContext,
}

impl ScrollAreaDispatcher {
    /// Create a new dispatcher with state setters
    pub fn new(
        set_scroll_position: StateSetter<ScrollPosition>,
        set_viewport_info: StateSetter<ViewportInfo>,
        set_scrollbar_visibility: StateSetter<ScrollbarVisibility>,
        context: ScrollAreaContext,
    ) -> Self {
        Self {
            set_scroll_position,
            set_viewport_info,
            set_scrollbar_visibility,
            context,
        }
    }

    /// Dispatch a scroll action
    pub fn dispatch(&self, action: ScrollAction) {
        match action {
            ScrollAction::SetVerticalScroll(position) => {
                let new_position = ScrollPosition {
                    vertical: position.min(self.context.max_vertical_scroll()),
                    horizontal: self.context.scroll_position.horizontal,
                };
                self.set_scroll_position.call(new_position.clone());

                // Trigger scroll callback if present
                if let Some(callback) = &self.context.on_scroll {
                    callback.emit((new_position.vertical, new_position.horizontal));
                }
            }
            ScrollAction::SetHorizontalScroll(position) => {
                let new_position = ScrollPosition {
                    vertical: self.context.scroll_position.vertical,
                    horizontal: position.min(self.context.max_horizontal_scroll()),
                };
                self.set_scroll_position.call(new_position.clone());

                // Trigger scroll callback if present
                if let Some(callback) = &self.context.on_scroll {
                    callback.emit((new_position.vertical, new_position.horizontal));
                }
            }
            ScrollAction::UpdateViewport(width, height) => {
                let new_viewport = ViewportInfo {
                    width,
                    height,
                    content_width: self.context.viewport_info.content_width,
                    content_height: self.context.viewport_info.content_height,
                };
                self.set_viewport_info.call(new_viewport);

                // Update scrollbar visibility
                let visibility = self.context.calculate_scrollbar_visibility();
                self.set_scrollbar_visibility.call(visibility);
            }
            ScrollAction::UpdateContent(width, height) => {
                let new_viewport = ViewportInfo {
                    width: self.context.viewport_info.width,
                    height: self.context.viewport_info.height,
                    content_width: width,
                    content_height: height,
                };
                self.set_viewport_info.call(new_viewport);

                // Update scrollbar visibility
                let visibility = self.context.calculate_scrollbar_visibility();
                self.set_scrollbar_visibility.call(visibility);
            }
            ScrollAction::ScrollBy(vertical_delta, horizontal_delta) => {
                let current = &self.context.scroll_position;
                let new_vertical = if vertical_delta < 0 {
                    current.vertical.saturating_sub((-vertical_delta) as usize)
                } else {
                    current.vertical + vertical_delta as usize
                };
                let new_horizontal = if horizontal_delta < 0 {
                    current
                        .horizontal
                        .saturating_sub((-horizontal_delta) as usize)
                } else {
                    current.horizontal + horizontal_delta as usize
                };

                let new_position = self
                    .context
                    .clamp_scroll_position(new_vertical, new_horizontal);
                self.set_scroll_position.call(new_position.clone());

                // Trigger scroll callback if present
                if let Some(callback) = &self.context.on_scroll {
                    callback.emit((new_position.vertical, new_position.horizontal));
                }
            }
            ScrollAction::ScrollTo(vertical, horizontal) => {
                let new_position = self.context.clamp_scroll_position(vertical, horizontal);
                self.set_scroll_position.call(new_position.clone());

                // Trigger scroll callback if present
                if let Some(callback) = &self.context.on_scroll {
                    callback.emit((new_position.vertical, new_position.horizontal));
                }
            }
        }
    }
}

/// Safe hook to access ScrollArea context with error handling
pub fn use_scroll_area_context_safe() -> ScrollAreaResult<ScrollAreaContext> {
    // Since use_context panics if not found, we need to use a different approach
    // For now, we'll use a try-catch pattern or provide a default context
    // In a real implementation, we'd need a non-panicking version of use_context

    // For demonstration, we'll return a default context with an error indicator
    // In practice, this would use a proper context lookup that returns Option<T>
    Err(ScrollAreaError::ContextNotFound)
}

/// Safe hook to access ScrollArea context provider with error handling
pub fn use_scroll_area_provider_safe(
    config: ScrollAreaConfig,
) -> ScrollAreaResult<ScrollAreaContext> {
    let (context, _state_setters) = use_scroll_area_provider(config);
    Ok(context)
}

/// Hook to safely dispatch ScrollArea actions with error handling
pub fn use_scroll_area_dispatch() -> ScrollAreaResult<impl Fn(ScrollAction) -> ScrollAreaResult<()>>
{
    let context_state = use_scroll_area_context_safe()?;

    Ok(move |action: ScrollAction| -> ScrollAreaResult<()> {
        // This would normally dispatch to a reducer, but for now we'll return Ok
        // In a full implementation, this would handle the action and update state
        match action {
            ScrollAction::SetVerticalScroll(position) => {
                // Validate position bounds
                let max_scroll = context_state.max_vertical_scroll();
                if position > max_scroll {
                    return Err(ScrollAreaError::InvalidScrollPosition {
                        position,
                        max_position: max_scroll,
                    });
                }
                Ok(())
            }
            ScrollAction::SetHorizontalScroll(position) => {
                // Validate position bounds
                let max_scroll = context_state.max_horizontal_scroll();
                if position > max_scroll {
                    return Err(ScrollAreaError::InvalidScrollPosition {
                        position,
                        max_position: max_scroll,
                    });
                }
                Ok(())
            }
            ScrollAction::UpdateViewport(width, height) => {
                if width == 0 || height == 0 {
                    return Err(ScrollAreaError::InvalidViewportDimensions { width, height });
                }
                Ok(())
            }
            ScrollAction::UpdateContent(_width, _height) => {
                // Content dimensions can be 0, so no validation needed
                Ok(())
            }
            ScrollAction::ScrollBy(_, _) | ScrollAction::ScrollTo(_, _) => {
                // These would be handled by the actual dispatcher
                Ok(())
            }
        }
    })
}
