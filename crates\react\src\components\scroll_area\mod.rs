//! ScrollArea and ScrollBar components for Terminus UI
//!
//! This module provides scrollable container components that follow Shadcn UI patterns
//! with professional TUI aesthetics and smooth scrolling behavior.
//!
//! The implementation uses a context-based architecture similar to React's Context API
//! for state management and component coordination.

use crossterm::event::{Event, KeyCode};
use ratatui::Frame;
use ratatui::prelude::Rect;
use ratatui::style::Color;
use ratatui::{
    layout::{Constraint, Direction},
    style::Style,
    widgets::Borders,
};
use terminus_ui_component_macro::component;
use terminus_ui_core::{
    Callback, Children, Element, FunctionalComponent, HasChildren, TrySetChildren, VirtualNode,
    use_event,
};
use terminus_ui_core::{LayoutProps, WidgetType};
use terminus_ui_props_macro::Props;

pub mod context;

// Re-export context types for convenience
pub use context::{
    ScrollAction, ScrollAreaConfig, ScrollAreaContext, ScrollAreaError, ScrollAreaResult,
    ScrollOrientation, ScrollPosition, ScrollbarPosition, ScrollbarVisibility, ViewportInfo,
    use_scroll_area_context, use_scroll_area_context_with_default, use_scroll_area_provider,
};

/// Props for the ScrollAreaViewport component
#[derive(Props, Debug, Clone)]
pub struct ScrollAreaViewportProps {
    /// Child components to render inside the viewport
    #[children]
    pub children: Children,
}

/// ScrollAreaViewport component - renders the scrollable content area
#[component(ScrollAreaViewport)]
pub fn scroll_area_viewport(props: ScrollAreaViewportProps, area: Rect) -> Element {
    // Get context from parent ScrollArea
    let context = use_scroll_area_context();

    // Calculate visible content based on scroll position
    let children_vec = flatten_children(&props.children);
    let vertical_offset = context.scroll_position.vertical;
    let horizontal_offset = context.scroll_position.horizontal;

    // Calculate viewport dimensions
    let viewport_height = area.height as usize;
    let _viewport_width = area.width as usize;

    // Apply vertical scrolling by skipping lines based on scroll position
    let mut visible_children: Vec<VirtualNode> = if context.config.orientation
        == ScrollOrientation::Vertical
        || context.config.orientation == ScrollOrientation::Both
    {
        children_vec
            .into_iter()
            .skip(vertical_offset)
            .take(viewport_height)
            .collect()
    } else {
        children_vec
    };

    // Apply horizontal scrolling by modifying text content
    if (context.config.orientation == ScrollOrientation::Horizontal
        || context.config.orientation == ScrollOrientation::Both)
        && horizontal_offset > 0
    {
        visible_children = visible_children
            .into_iter()
            .map(|child| match child {
                VirtualNode::Widget {
                    widget_type: WidgetType::Text,
                    props: text_props,
                    children,
                } => {
                    if let Some(text_props) = text_props.get::<terminus_ui_core::TextProps>() {
                        let content = &text_props.content;
                        let scrolled_content = if content.len() > horizontal_offset {
                            content.chars().skip(horizontal_offset).collect::<String>()
                        } else {
                            String::new()
                        };

                        VirtualNode::widget(
                            WidgetType::Text,
                            terminus_ui_core::TextProps {
                                content: scrolled_content,
                                style: text_props.style,
                            },
                            children,
                        )
                    } else {
                        VirtualNode::Widget {
                            widget_type: WidgetType::Text,
                            props: text_props,
                            children,
                        }
                    }
                }
                _ => child,
            })
            .collect();
    }

    // Create layout for visible content
    let visible_count = visible_children.len();
    let content_constraints = vec![Constraint::Length(1); visible_count];

    VirtualNode::widget(
        WidgetType::Layout,
        LayoutProps {
            direction: Some(Direction::Vertical),
            constraints: Some(content_constraints),
            margin: None,
        },
        visible_children,
    )
}

/// Props for the ScrollAreaScrollbar component
#[derive(Props, Debug, Clone)]
pub struct ScrollAreaScrollbarProps {
    /// Orientation of the scrollbar
    pub orientation: ScrollOrientation,
    /// Custom begin symbol (e.g., "↑" for vertical, "←" for horizontal)
    pub begin_symbol: Option<String>,
    /// Custom end symbol (e.g., "↓" for vertical, "→" for horizontal)
    pub end_symbol: Option<String>,
    /// Custom thumb symbol for the scrollbar handle
    pub thumb_symbol: Option<String>,
    /// Custom track symbol for the scrollbar track
    pub track_symbol: Option<String>,
    /// Style for the scrollbar
    pub style: Option<Style>,
    /// Override scroll position for immediate updates (bypasses context)
    pub scroll_position_override: Option<ScrollPosition>,
    /// Override viewport info for immediate updates (bypasses context)
    pub viewport_info_override: Option<ViewportInfo>,
}

/// ScrollAreaScrollbar component - renders scrollbars that consume context
#[component(ScrollAreaScrollbar)]
pub fn scroll_area_scrollbar(props: ScrollAreaScrollbarProps) -> Element {
    // Get context from parent ScrollArea, but allow overrides for immediate updates
    let base_context = use_scroll_area_context();

    // Use override values if provided, otherwise use context values
    let scroll_position = props
        .scroll_position_override
        .unwrap_or(base_context.scroll_position);
    let viewport_info = props
        .viewport_info_override
        .unwrap_or(base_context.viewport_info);

    // Create effective context with potentially overridden values
    let context = ScrollAreaContext {
        scroll_position,
        viewport_info,
        scrollbar_visibility: base_context.scrollbar_visibility,
        config: base_context.config,
        on_scroll: base_context.on_scroll,
    };

    // Determine if this scrollbar should be visible
    let is_visible = match props.orientation {
        ScrollOrientation::Vertical => context.scrollbar_visibility.vertical,
        ScrollOrientation::Horizontal => context.scrollbar_visibility.horizontal,
        ScrollOrientation::Both => {
            context.scrollbar_visibility.vertical || context.scrollbar_visibility.horizontal
        }
    };

    if !is_visible {
        return VirtualNode::widget(
            WidgetType::Text,
            terminus_ui_core::TextProps {
                content: "".to_string(),
                style: None,
            },
            vec![],
        );
    }

    // We'll get the actual scroll position directly from context instead of using ratios

    // Create scrollbar using the existing ratatui scrollbar widget
    use ratatui::widgets::ScrollbarOrientation;

    let scrollbar_orientation = match props.orientation {
        ScrollOrientation::Vertical => ScrollbarOrientation::VerticalRight,
        ScrollOrientation::Horizontal => ScrollbarOrientation::HorizontalBottom,
        ScrollOrientation::Both => ScrollbarOrientation::VerticalRight, // Default to vertical
    };

    // Get the actual scroll position (not a ratio) for proper thumb positioning
    let position = if props.orientation == ScrollOrientation::Vertical {
        context.scroll_position.vertical as u16
    } else {
        context.scroll_position.horizontal as u16
    };

    let content_length = if props.orientation == ScrollOrientation::Vertical {
        context.viewport_info.content_height as u16
    } else {
        context.viewport_info.content_width as u16
    };
    let viewport_length = if props.orientation == ScrollOrientation::Vertical {
        context.viewport_info.height as u16
    } else {
        context.viewport_info.width as u16
    };

    VirtualNode::widget(
        WidgetType::Scrollbar,
        terminus_ui_core::ScrollbarProps {
            orientation: scrollbar_orientation,
            position,
            content_length,
            viewport_length,
            style: props.style,
            begin_symbol: props.begin_symbol,
            end_symbol: props.end_symbol,
            thumb_symbol: Some(props.thumb_symbol.unwrap_or_else(|| "█".to_string())),
            track_symbol: Some(props.track_symbol.unwrap_or_else(|| {
                if props.orientation == ScrollOrientation::Vertical {
                    "│".to_string()
                } else {
                    "─".to_string()
                }
            })),
        },
        vec![],
    )
}

/// Props for the ScrollArea component
///
/// This is the main scrollable container component that manages content overflow
/// and provides smooth scrolling behavior with optional scrollbars.
#[derive(Props, Debug, Clone)]
pub struct ScrollAreaProps {
    /// Scroll orientation support
    pub orientation: ScrollOrientation,
    /// Whether to show scrollbars
    pub show_scrollbars: bool,
    /// Position of vertical scrollbar
    pub vertical_scrollbar_position: ScrollbarPosition,
    /// Position of horizontal scrollbar  
    pub horizontal_scrollbar_position: ScrollbarPosition,
    /// Custom scrollbar style
    pub scrollbar_style: Option<Style>,
    /// Scroll step size for keyboard navigation
    pub scroll_step: usize,
    /// Whether content should wrap
    pub wrap: bool,
    /// Callback for scroll events
    pub on_scroll: Option<Callback<(usize, usize)>>, // (vertical_offset, horizontal_offset)
    /// Custom begin symbols for scrollbars
    pub scrollbar_begin_symbols: Option<(String, String)>, // (vertical, horizontal)
    /// Custom end symbols for scrollbars
    pub scrollbar_end_symbols: Option<(String, String)>, // (vertical, horizontal)
    /// Block properties for the container
    pub title: Option<String>,
    pub borders: Option<Borders>,
    pub border_style: Option<Style>,
    /// Child components to render inside the scroll area
    #[children]
    pub children: Children,
}

/// ScrollArea component implementation
///
/// Main scrollable container component that manages content overflow and provides
/// smooth scrolling behavior with optional scrollbars following Shadcn UI patterns.
#[component(ScrollArea)]
pub fn scroll_area(props: ScrollAreaProps, area: Rect) -> Element {
    // Create configuration from props
    let config = ScrollAreaConfig {
        orientation: props.orientation,
        show_scrollbars: props.show_scrollbars,
        scroll_step: if props.scroll_step == 0 {
            1
        } else {
            props.scroll_step
        },
        wrap: props.wrap,
        vertical_scrollbar_position: props.vertical_scrollbar_position,
        horizontal_scrollbar_position: props.horizontal_scrollbar_position,
    };

    // Initialize context provider with error handling
    let (context, state_setters) = use_scroll_area_provider(config);

    // Validate viewport dimensions
    if area.width == 0 || area.height == 0 {
        // Return error component for invalid dimensions
        return VirtualNode::widget(
            WidgetType::Text,
            terminus_ui_core::TextProps {
                content: format!(
                    "ScrollArea Error: Invalid viewport dimensions {}x{}",
                    area.width, area.height
                ),
                style: Some(Style::default().fg(Color::Red)),
            },
            vec![],
        );
    }

    // Calculate content dimensions from children
    let children_vec = flatten_children(&props.children);
    let content_height = children_vec.len();
    let content_width = children_vec
        .iter()
        .filter_map(|child| {
            if let VirtualNode::Widget {
                props: child_props, ..
            } = child
            {
                child_props
                    .get::<terminus_ui_core::TextProps>()
                    .map(|text_props| text_props.content.len())
            } else {
                None
            }
        })
        .max()
        .unwrap_or(0);

    // Calculate viewport dimensions
    let viewport_height = (area.height as usize).saturating_sub(4); // Account for borders
    let viewport_width = (area.width as usize).saturating_sub(3); // Account for borders and scrollbar

    // Update context with current viewport and content dimensions
    let updated_viewport_info = ViewportInfo {
        width: viewport_width,
        height: viewport_height,
        content_width,
        content_height,
    };
    state_setters
        .set_viewport_info
        .call(updated_viewport_info.clone());

    // Calculate and update scrollbar visibility
    let mut temp_context = context.clone();
    temp_context.viewport_info = updated_viewport_info.clone();
    let scrollbar_visibility = temp_context.calculate_scrollbar_visibility();
    state_setters
        .set_scrollbar_visibility
        .call(scrollbar_visibility.clone());

    // Create updated context for local calculations
    let context = ScrollAreaContext {
        viewport_info: updated_viewport_info,
        scrollbar_visibility,
        ..context
    };

    // Handle keyboard events for scrolling using context
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            let step_size = context.config.scroll_step;
            match key.code {
                KeyCode::Up | KeyCode::Char('k') => {
                    if context.config.orientation == ScrollOrientation::Vertical
                        || context.config.orientation == ScrollOrientation::Both
                    {
                        let new_scroll = context.scroll_position.vertical.saturating_sub(step_size);
                        // Update scroll position through state setter
                        let new_position = ScrollPosition {
                            vertical: new_scroll,
                            horizontal: context.scroll_position.horizontal,
                        };
                        state_setters.set_scroll_position.call(new_position);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, context.scroll_position.horizontal));
                        }
                    }
                }
                KeyCode::Down | KeyCode::Char('j') => {
                    if context.config.orientation == ScrollOrientation::Vertical
                        || context.config.orientation == ScrollOrientation::Both
                    {
                        let max_scroll = content_height.saturating_sub(viewport_height);
                        let new_scroll =
                            (context.scroll_position.vertical + step_size).min(max_scroll);
                        // Update scroll position through state setter
                        let new_position = ScrollPosition {
                            vertical: new_scroll,
                            horizontal: context.scroll_position.horizontal,
                        };
                        state_setters.set_scroll_position.call(new_position);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, context.scroll_position.horizontal));
                        }
                    }
                }
                KeyCode::Left | KeyCode::Char('h') => {
                    if context.config.orientation == ScrollOrientation::Horizontal
                        || context.config.orientation == ScrollOrientation::Both
                    {
                        let new_scroll =
                            context.scroll_position.horizontal.saturating_sub(step_size);
                        // Update scroll position through state setter
                        let new_position = ScrollPosition {
                            vertical: context.scroll_position.vertical,
                            horizontal: new_scroll,
                        };
                        state_setters.set_scroll_position.call(new_position);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((context.scroll_position.vertical, new_scroll));
                        }
                    }
                }
                KeyCode::Right | KeyCode::Char('l') => {
                    if context.config.orientation == ScrollOrientation::Horizontal
                        || context.config.orientation == ScrollOrientation::Both
                    {
                        let max_scroll = content_width.saturating_sub(viewport_width);
                        let new_scroll =
                            (context.scroll_position.horizontal + step_size).min(max_scroll);
                        // Update scroll position through state setter
                        let new_position = ScrollPosition {
                            vertical: context.scroll_position.vertical,
                            horizontal: new_scroll,
                        };
                        state_setters.set_scroll_position.call(new_position);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((context.scroll_position.vertical, new_scroll));
                        }
                    }
                }
                KeyCode::PageUp => {
                    if context.config.orientation == ScrollOrientation::Vertical
                        || context.config.orientation == ScrollOrientation::Both
                    {
                        let new_scroll = context
                            .scroll_position
                            .vertical
                            .saturating_sub(step_size * 10);
                        // Update scroll position through state setter
                        let new_position = ScrollPosition {
                            vertical: new_scroll,
                            horizontal: context.scroll_position.horizontal,
                        };
                        state_setters.set_scroll_position.call(new_position);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, context.scroll_position.horizontal));
                        }
                    }
                }
                KeyCode::PageDown => {
                    if context.config.orientation == ScrollOrientation::Vertical
                        || context.config.orientation == ScrollOrientation::Both
                    {
                        let max_scroll = content_height.saturating_sub(viewport_height);
                        let new_scroll =
                            (context.scroll_position.vertical + step_size * 10).min(max_scroll);
                        // Update scroll position through state setter
                        let new_position = ScrollPosition {
                            vertical: new_scroll,
                            horizontal: context.scroll_position.horizontal,
                        };
                        state_setters.set_scroll_position.call(new_position);
                        if let Some(callback) = &props.on_scroll {
                            callback.emit((new_scroll, context.scroll_position.horizontal));
                        }
                    }
                }
                _ => {}
            }
        }
    }

    // Create viewport component with children
    let viewport = scroll_area_viewport(
        ScrollAreaViewportProps {
            children: props.children.clone(),
        },
        area,
    );

    // Create horizontal layout for content and vertical scrollbar
    let mut horizontal_children = vec![viewport];

    // Add vertical scrollbar if needed
    if context.scrollbar_visibility.vertical {
        let vertical_scrollbar = scroll_area_scrollbar(ScrollAreaScrollbarProps {
            orientation: ScrollOrientation::Vertical,
            begin_symbol: props
                .scrollbar_begin_symbols
                .as_ref()
                .map(|(v, _)| v.clone()),
            end_symbol: props.scrollbar_end_symbols.as_ref().map(|(v, _)| v.clone()),
            thumb_symbol: Some("█".to_string()),
            track_symbol: Some("│".to_string()),
            style: props.scrollbar_style,
            // Pass current context values for immediate updates
            scroll_position_override: Some(context.scroll_position.clone()),
            viewport_info_override: Some(context.viewport_info.clone()),
        });
        horizontal_children.push(vertical_scrollbar);
    }

    // Create horizontal layout (content + vertical scrollbar)
    let horizontal_layout = VirtualNode::widget(
        WidgetType::Layout,
        LayoutProps {
            direction: Some(Direction::Horizontal),
            constraints: Some(if context.scrollbar_visibility.vertical {
                vec![
                    Constraint::Min(0),    // Content area
                    Constraint::Length(1), // Vertical scrollbar
                ]
            } else {
                vec![Constraint::Min(0)] // Just content
            }),
            margin: None,
        },
        horizontal_children,
    );

    // Create main vertical layout children
    let mut main_children = vec![horizontal_layout];

    // Add horizontal scrollbar if needed
    if context.scrollbar_visibility.horizontal {
        let horizontal_scrollbar = scroll_area_scrollbar(ScrollAreaScrollbarProps {
            orientation: ScrollOrientation::Horizontal,
            begin_symbol: props
                .scrollbar_begin_symbols
                .as_ref()
                .map(|(_, h)| h.clone()),
            end_symbol: props.scrollbar_end_symbols.as_ref().map(|(_, h)| h.clone()),
            thumb_symbol: Some("█".to_string()),
            track_symbol: Some("─".to_string()),
            style: props.scrollbar_style,
            // Pass current context values for immediate updates
            scroll_position_override: Some(context.scroll_position.clone()),
            viewport_info_override: Some(context.viewport_info.clone()),
        });
        main_children.push(horizontal_scrollbar);
    }

    // Create main vertical layout
    let content_layout = VirtualNode::widget(
        WidgetType::Layout,
        LayoutProps {
            direction: Some(Direction::Vertical),
            constraints: Some(if context.scrollbar_visibility.horizontal {
                vec![
                    Constraint::Min(0),    // Content + vertical scrollbar
                    Constraint::Length(1), // Horizontal scrollbar
                ]
            } else {
                vec![Constraint::Min(0)] // Just content + vertical scrollbar
            }),
            margin: None,
        },
        main_children,
    );

    // Add scroll indicator text for debugging
    let scroll_indicator = VirtualNode::widget(
        WidgetType::Text,
        terminus_ui_core::TextProps {
            content: format!(
                "V:{}/{} H:{}/{} | CH:{} VH:{} CW:{} VW:{} | VScroll:{} HScroll:{}",
                context.scroll_position.vertical,
                context.max_vertical_scroll(),
                context.scroll_position.horizontal,
                context.max_horizontal_scroll(),
                content_height,
                viewport_height,
                content_width,
                viewport_width,
                context.scrollbar_visibility.vertical,
                context.scrollbar_visibility.horizontal
            ),
            style: None,
        },
        vec![],
    );

    // Create final layout with content and debug info
    let final_layout = VirtualNode::widget(
        WidgetType::Layout,
        LayoutProps {
            direction: Some(Direction::Vertical),
            constraints: Some(vec![
                Constraint::Min(0),    // Content area with scrollbars
                Constraint::Length(1), // Scroll indicator
            ]),
            margin: None,
        },
        vec![content_layout, scroll_indicator],
    );

    // Create main container block
    VirtualNode::widget(
        WidgetType::Block,
        terminus_ui_core::BlockProps {
            title: props.title.clone(),
            borders: props.borders,
            border_style: props.border_style,
        },
        vec![final_layout],
    )
}

/// Flatten children to handle Vec<Element> expressions that get wrapped in Layout
/// This ensures ScrollArea can properly count and process individual elements
fn flatten_children(children: &Children) -> Vec<VirtualNode> {
    let mut flattened = Vec::new();

    for child in children.iter() {
        match child {
            // If child is a Layout with vertical direction (created by Vec<Element>), flatten its children
            VirtualNode::Widget {
                widget_type,
                props,
                children: layout_children,
            } if *widget_type == WidgetType::Layout => {
                // Check if this is a vertical layout (likely from Vec<Element>)
                if let Some(layout_props) = props.get::<LayoutProps>() {
                    if layout_props.direction == Some(ratatui::layout::Direction::Vertical) {
                        // Flatten the layout children
                        flattened.extend(layout_children.iter().cloned());
                        continue;
                    }
                }
                // If not a vertical layout, keep as is
                flattened.push(child.clone());
            }
            // For all other children, keep as is
            _ => flattened.push(child.clone()),
        }
    }

    flattened
}
