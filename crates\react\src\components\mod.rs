//! React-like components for Terminus UI
//!
//! This module provides higher-level components that can be used with the rsx! macro
//! and follow familiar React/Shadcn UI patterns.

pub mod layout;
pub mod modal;
pub mod scroll_area;

// Re-export components for easier access
pub use layout::{Layout, LayoutProps};
pub use modal::{
    Dialog, DialogContent, DialogContentProps, DialogProps, DialogTitle, DialogTitleProps,
};
pub use scroll_area::{
    ScrollArea, ScrollAreaProps, ScrollAreaScrollbar, ScrollAreaScrollbarProps, ScrollAreaViewport,
    ScrollAreaViewportProps, ScrollOrientation, ScrollbarPosition,
};
