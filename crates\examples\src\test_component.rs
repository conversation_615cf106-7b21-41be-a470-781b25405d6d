use terminus_ui::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct TestProps {
    pub message: String,
}

#[component(TestComponent)]
fn test_component(props: TestProps) -> Element {
    rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![Constraint::Min(1)]}>
            <Text content={props.message} />
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <TestComponent message={"Hello".to_string()} />
    };

    render(element)
}
