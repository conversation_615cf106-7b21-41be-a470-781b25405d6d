use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Professional demonstration of the new ScrollArea context-based architecture
/// This example showcases the new error handling, context system, and composable components

#[derive(Props, Debug, Clone)]
pub struct ScrollDemoProps {
    pub title: String,
}

/// Main demo component showcasing the new ScrollArea architecture
#[component(ScrollDemo)]
fn scroll_demo(props: ScrollDemoProps) -> Element {
    let (selected_demo, set_selected_demo) = use_state(0usize);
    let (scroll_callback_info, set_scroll_callback_info) =
        use_state("No scroll events yet".to_string());
    let (error_info, set_error_info) = use_state("No errors".to_string());
    let (context_info, set_context_info) = use_state("Context not accessed".to_string());

    // Handle demo navigation
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            match key.code {
                KeyCode::Tab => {
                    let next_demo = (selected_demo.get() + 1) % 6; // Updated for 6 demos
                    set_selected_demo.call(next_demo);
                }
                KeyCode::BackTab => {
                    let prev_demo = if selected_demo.get() == 0 {
                        5 // Updated for 6 demos
                    } else {
                        selected_demo.get() - 1
                    };
                    set_selected_demo.call(prev_demo);
                }
                KeyCode::Char('r') => {
                    // Reset error and context info
                    set_error_info.call("No errors".to_string());
                    set_context_info.call("Context not accessed".to_string());
                }
                _ => {}
            }
        }
    }

    // Professional scroll callback with error handling
    let on_scroll = {
        let set_info = set_scroll_callback_info.clone();
        let set_error = set_error_info.clone();
        move |pos: (usize, usize)| match (pos.0, pos.1) {
            (y, x) if y > 1000 || x > 1000 => {
                set_error.call(format!("Warning: Large scroll position Y={}, X={}", y, x));
            }
            (y, x) => {
                set_info.call(format!(
                    "✓ Scroll position: Y={}, X={} (Professional callback)",
                    y, x
                ));
                set_error.call("No errors".to_string());
            }
        }
    };

    rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(4),  // Header
            Constraint::Min(0),     // Content
            Constraint::Length(6),  // Status footer
        ]}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content={format!("🚀 Professional ScrollArea Demo {}/6 - Context-Based Architecture", selected_demo.get() + 1)} />
                    <Text content="Tab/Shift+Tab: Navigate | R: Reset Status | Q/Esc: Quit" />
                </Layout>
            </Block>

            // Main content area
            {match selected_demo.get() {
                0 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // Demo 1A: Context-Based Architecture
                        <ScrollArea
                            title={Some("🔄 Context-Based Architecture".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Green))}
                            scroll_step={1}
                        >
                            <Text content="🚀 NEW: Context-Based ScrollArea Architecture" />
                            <Text content="✨ Professional state management with React patterns" />
                            <Text content="🔧 ScrollAreaContext manages all component state" />
                            <Text content="📊 Viewport info: dimensions, content size, scroll position" />
                            <Text content="🎯 ScrollbarVisibility: automatic show/hide logic" />
                            <Text content="⚙️ ScrollAreaConfig: centralized configuration" />
                            <Text content="🔄 ScrollPosition: vertical and horizontal tracking" />
                            <Text content="📡 Event callbacks: on_scroll with position data" />
                            <Text content="🛡️ Error handling: bounds checking and validation" />
                            <Text content="🧩 Composable: ScrollAreaViewport + ScrollAreaScrollbar" />
                            <Text content="🎨 Professional TUI aesthetics with smooth scrolling" />
                            <Text content="⌨️ Keyboard navigation: Arrow keys, Page Up/Down" />
                            <Text content="🖱️ Mouse support: Wheel scrolling (future)" />
                            <Text content="📏 Dynamic sizing: Responds to terminal resize" />
                            <Text content="🔍 Scroll indicators: Visual feedback for position" />
                            <Text content="⚡ Performance: Optimized rendering and state updates" />
                            <Text content="🧪 Testable: Clean separation of concerns" />
                            <Text content="📚 Documentation: Comprehensive API reference" />
                            <Text content="🔒 Type safety: Rust's type system prevents errors" />
                            <Text content="🎯 This demonstrates the new architecture in action!" />
                            <Text content="📋 ScrollAreaContextState: Provider state management" />
                            <Text content="🔗 use_scroll_area_provider: Context initialization hook" />
                            <Text content="🎣 use_scroll_area_context: Consumer access hook" />
                            <Text content="⚠️ ScrollAreaError: Professional error types" />
                            <Text content="✅ ScrollAreaResult: Type-safe error handling" />
                            <Text content="🔄 ScrollAction: Dispatch-based state updates" />
                            <Text content="🎛️ Safe bounds checking: Prevents invalid scroll positions" />
                            <Text content="📐 Dimension validation: Ensures valid viewport sizes" />
                            <Text content="🔧 Context access validation: Graceful error handling" />
                            <Text content="🎯 This is the future of TUI component architecture!" />
                        </ScrollArea>

                        // Demo 1B: Error Handling Showcase
                        <ScrollArea
                            title={Some("🛡️ Professional Error Handling".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Magenta))}
                            scroll_step={1}
                        >
                            <Text content="🛡️ PROFESSIONAL ERROR HANDLING SYSTEM" />
                            <Text content="⚠️ ScrollAreaError::ContextNotFound" />
                            <Text content="📍 ScrollAreaError::InvalidScrollPosition" />
                            <Text content="📐 ScrollAreaError::InvalidViewportDimensions" />
                            <Text content="📊 ScrollAreaError::InvalidContentDimensions" />
                            <Text content="✅ ScrollAreaResult<T> for type-safe operations" />
                            <Text content="🔒 Bounds checking prevents scroll overflow" />
                            <Text content="🎯 Dimension validation ensures valid viewports" />
                            <Text content="🔄 Safe context access with error recovery" />
                            <Text content="📋 Professional error messages with context" />
                            <Text content="🧪 Testable error conditions and recovery" />
                            <Text content="⚡ Graceful degradation on errors" />
                            <Text content="🎨 Error components with visual feedback" />
                            <Text content="🔧 Developer-friendly error debugging" />
                            <Text content="📚 Comprehensive error documentation" />
                        </ScrollArea>
                    </Layout>
                },
                1 => rsx! {
                    <ScrollArea
                        title={Some("🧩 Horizontal + Vertical Scrolling Demo".to_string())}
                        orientation={ScrollOrientation::Both}
                        show_scrollbars={true}
                        borders={Some(Borders::ALL)}
                        border_style={Some(Style::default().fg(Color::Yellow))}
                        scroll_step={1}
                    >
                        <Text content="🧩 HORIZONTAL + VERTICAL SCROLLING DEMO - This line is intentionally very long to demonstrate horizontal scrolling functionality in the ScrollArea component. Use Left/Right arrow keys to scroll horizontally and test smooth thumb movement!" />
                        <Text content="📦 ScrollArea: Main container component - Another very long line that exceeds typical terminal width (120+ characters) to test horizontal scrollbar positioning and smooth proportional movement with keyboard navigation controls." />
                        <Text content="🖼️ ScrollAreaViewport: Content rendering area - Testing horizontal scroll with content that is much wider than the viewport to ensure proper thumb positioning and proportional movement in both directions simultaneously." />
                        <Text content="📊 ScrollAreaScrollbar: Visual scroll indicators - This demonstrates both vertical and horizontal scrollbars working together with independent positioning and smooth movement characteristics for professional TUI experience." />
                        <Text content="🔗 Context sharing between all components - The context system properly manages both vertical and horizontal scroll positions with state synchronization across all child components and real-time updates." />
                        <Text content="⚙️ ScrollAreaConfig: Centralized configuration - Configuration supports both orientations with proper bounds checking and validation for both horizontal and vertical scroll limits with step size control." />
                        <Text content="📍 ScrollPosition: Shared position state - Both vertical and horizontal positions are tracked independently with proper state management and callback integration for real-time position updates and smooth scrolling." />
                        <Text content="👁️ ScrollbarVisibility: Dynamic show/hide logic - Scrollbars appear automatically when content exceeds viewport dimensions in either direction, with proper visibility calculations and responsive behavior." />
                        <Text content="📐 ViewportInfo: Dimensions and content tracking - Viewport tracks both width and height with content dimensions calculated from the maximum line length and total line count for accurate scrolling bounds." />
                        <Text content="🎯 Clean separation of concerns - Each component has a specific responsibility with proper encapsulation and clear interfaces for state management and event handling in both scroll directions." />
                        <Text content="🔄 React-style state management patterns - State updates use React-like hooks with proper dependency tracking and efficient re-rendering when scroll positions change in either vertical or horizontal direction." />
                        <Text content="🎨 Professional component composition - Components can be composed together with proper context sharing and independent configuration while maintaining consistent behavior across both scroll orientations." />
                        <Text content="⚡ Efficient state updates and rendering - State changes are batched and optimized to minimize re-renders while maintaining smooth scrolling performance in both directions with proper synchronization." />
                        <Text content="🧪 Testable component architecture - The architecture supports comprehensive testing with proper mocking and isolation of components for reliable test execution in both horizontal and vertical scrolling scenarios." />
                        <Text content="� KEYBOARD CONTROLS: ↑↓ for vertical scrolling, ←→ for horizontal scrolling - Test the smooth proportional movement of both scrollbar thumbs as you navigate through the content in both directions!" />
                        <Text content="📏 CONTENT WIDTH TEST: This line is exactly 200 characters long to test precise horizontal scrolling calculations and ensure proper thumb positioning at specific scroll positions in the horizontal track." />
                        <Text content="🎛️ SCROLL STEP CONFIGURATION: The scroll_step prop controls movement speed - each key press moves by the configured step size with proper bounds checking to prevent overflow in both directions." />
                        <Text content="🔍 PROPORTIONAL MOVEMENT TEST: With content wider than viewport, each horizontal key press should move the thumb proportionally - test by counting key presses from start to end of horizontal track." />
                        <Text content="✨ PROFESSIONAL TUI EXPERIENCE: Smooth scrolling, responsive controls, visual feedback, and professional aesthetics combine to create an excellent terminal user interface experience with dual-axis navigation." />
                        <Text content="🎯 DUAL-AXIS TEST: Try scrolling to the bottom-right corner to see both scrollbars at maximum position, then return to top-left to verify proper bounds and positioning in both dimensions simultaneously." />
                    </ScrollArea>
                },
                2 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // Performance Optimizations Demo
                        <ScrollArea
                            title={Some("⚡ Performance Optimizations".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Blue))}
                            scroll_step={2}
                        >
                            <Text content="⚡ PERFORMANCE OPTIMIZATION FEATURES" />
                            <Text content="🚀 Context caching for efficient state access" />
                            <Text content="🧠 Memoization of expensive calculations" />
                            <Text content="🔄 Optimized state updates to minimize re-renders" />
                            <Text content="📊 Efficient scroll position calculations" />
                            <Text content="🎯 Smart scrollbar visibility detection" />
                            <Text content="⚡ Lazy evaluation of viewport dimensions" />
                            <Text content="🔧 Batched state updates for smooth scrolling" />
                            <Text content="📐 Optimized content dimension tracking" />
                            <Text content="🎨 Efficient rendering pipeline" />
                            <Text content="💾 Memory-efficient content handling" />
                            <Text content="🔍 Smart bounds checking algorithms" />
                            <Text content="⏱️ Debounced scroll event handling" />
                            <Text content="🧩 Component-level optimization strategies" />
                            <Text content="📈 Performance monitoring and metrics" />
                        </ScrollArea>

                        // Configuration Showcase
                        <ScrollArea
                            title={Some("⚙️ Configuration Options".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={false}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Red))}
                            scroll_step={1}
                        >
                            <Text content="⚙️ COMPREHENSIVE CONFIGURATION SYSTEM" />
                            <Text content="🎛️ ScrollOrientation: Vertical, Horizontal, Both" />
                            <Text content="👁️ show_scrollbars: Dynamic visibility control" />
                            <Text content="📍 ScrollbarPosition: Left, Right, Top, Bottom" />
                            <Text content="⚡ scroll_step: Customizable movement speed" />
                            <Text content="🔄 wrap: Content wrapping behavior" />
                            <Text content="🎨 Custom scrollbar symbols and styling" />
                            <Text content="📏 Dynamic viewport dimension handling" />
                            <Text content="🔧 Professional configuration patterns" />
                            <Text content="📋 Type-safe configuration validation" />
                            <Text content="🎯 Default values with override capability" />
                            <Text content="🧪 Testable configuration scenarios" />
                            <Text content="📚 Well-documented configuration options" />
                            <Text content="🔒 Immutable configuration for safety" />
                            <Text content="🌟 This demo shows scrollbars disabled!" />
                        </ScrollArea>
                    </Layout>
                },
                3 => rsx! {
                    <Layout direction={Direction::Vertical} constraints={vec![
                        Constraint::Length(3),
                        Constraint::Min(0),
                    ]}>
                        // Callback info
                        <Block
                            title={"📡 Professional Callback System".to_string()}
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::White)}
                        >
                            <Text content={scroll_callback_info.get()} />
                        </Block>

                        // Scrollable area with callback
                        <ScrollArea
                            title={Some("📡 Scroll to see professional callbacks in action".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Cyan))}
                            scroll_step={1}
                            on_scroll={on_scroll}
                        >
                            <Text content="📡 PROFESSIONAL CALLBACK SYSTEM" />
                            <Text content="🎯 on_scroll: Real-time position tracking" />
                            <Text content="📊 Position data: (vertical, horizontal) coordinates" />
                            <Text content="⚡ Event-driven architecture for responsiveness" />
                            <Text content="🔄 Callback integration with context system" />
                            <Text content="🛡️ Error handling in callback execution" />
                            <Text content="📈 Performance monitoring via callbacks" />
                            <Text content="🎨 UI updates based on scroll position" />
                            <Text content="🔧 Debounced callbacks for smooth performance" />
                            <Text content="📋 Type-safe callback signatures" />
                            <Text content="🧪 Testable callback behavior" />
                            <Text content="📚 Well-documented callback patterns" />
                            <Text content="🎛️ Configurable callback frequency" />
                            <Text content="🌟 Professional event handling architecture" />
                            <Text content="⬆️⬇️ Scroll to see the callback info update above!" />
                        </ScrollArea>
                    </Layout>
                },
                4 => rsx! {
                    <Layout direction={Direction::Vertical} constraints={vec![
                        Constraint::Length(4),
                        Constraint::Min(0),
                    ]}>
                        // Error status display
                        <Block
                            title={"🛡️ Error Handling Status".to_string()}
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::Red)}
                        >
                            <Layout direction={Direction::Vertical}>
                                <Text content={error_info.get()} />
                                <Text content={context_info.get()} />
                            </Layout>
                        </Block>

                        // Error demonstration area
                        <ScrollArea
                            title={Some("🧪 Error Handling Test Area".to_string())}
                            orientation={ScrollOrientation::Vertical}
                            show_scrollbars={true}
                            borders={Some(Borders::ALL)}
                            border_style={Some(Style::default().fg(Color::Red))}
                            scroll_step={1}
                        >
                            <Text content="🧪 ERROR HANDLING DEMONSTRATION" />
                            <Text content="🛡️ This demo shows professional error handling" />
                            <Text content="⚠️ ScrollAreaError types and recovery patterns" />
                            <Text content="✅ ScrollAreaResult for type-safe operations" />
                            <Text content="🔒 Bounds checking prevents invalid operations" />
                            <Text content="📐 Dimension validation ensures valid viewports" />
                            <Text content="🎯 Context access validation with graceful fallbacks" />
                            <Text content="📋 Professional error messages with context" />
                            <Text content="🔄 Error recovery and graceful degradation" />
                            <Text content="🧪 Testable error conditions and scenarios" />
                            <Text content="📚 Comprehensive error documentation" />
                            <Text content="🔧 Developer-friendly error debugging tools" />
                            <Text content="⚡ Performance-optimized error handling" />
                            <Text content="🎨 Visual error feedback in the UI" />
                            <Text content="🌟 Professional error handling architecture!" />
                        </ScrollArea>
                    </Layout>
                },
                5 => rsx! {
                    <ScrollArea
                        title={Some("🚀 Future Roadmap & Features".to_string())}
                        orientation={ScrollOrientation::Both}
                        show_scrollbars={true}
                        borders={Some(Borders::ALL)}
                        border_style={Some(Style::default().fg(Color::Green))}
                        scroll_step={1}
                    >
                        <Text content="🚀 FUTURE ROADMAP & ADVANCED FEATURES" />
                        <Text content="🖱️ Mouse wheel scrolling support" />
                        <Text content="📱 Touch gesture support for modern terminals" />
                        <Text content="🎨 Advanced theming and customization options" />
                        <Text content="⚡ Virtual scrolling for massive datasets" />
                        <Text content="🔍 Search and highlight functionality" />
                        <Text content="📊 Scroll position persistence and restoration" />
                        <Text content="🎯 Smooth scrolling animations" />
                        <Text content="📐 Auto-sizing and responsive design improvements" />
                        <Text content="🧩 Plugin system for extensibility" />
                        <Text content="📈 Performance monitoring and analytics" />
                        <Text content="🔧 Advanced configuration options" />
                        <Text content="🧪 Comprehensive testing framework" />
                        <Text content="📚 Interactive documentation and examples" />
                        <Text content="🌐 Cross-platform compatibility enhancements" />
                        <Text content="🎪 Demo applications and showcases" />
                        <Text content="👥 Community contributions and feedback integration" />
                        <Text content="🔄 Continuous integration and deployment" />
                        <Text content="📦 Package management and distribution" />
                        <Text content="🎯 This represents the future of TUI components!" />
                        <Text content="🌟 Professional, performant, and developer-friendly!" />
                    </ScrollArea>
                },
                _ => rsx! {
                    <Block
                        title={"❌ Invalid Demo Selection".to_string()}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Red)}
                    >
                        <Text content="❌ Invalid demo selection. Use Tab/Shift+Tab to navigate." />
                    </Block>
                },
            }}

            // Professional status footer
            <Block
                title={"🎮 Professional Controls & Status".to_string()}
                borders={Borders::TOP}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="🎮 Controls: ↑/↓/←/→ = Scroll | Page Up/Down = Fast | Home/End = Jump | Tab = Next Demo | R = Reset | Q/Esc = Quit" />
                    <Text content={format!("📊 Status: Demo {}/6 | Scroll: {} | Error: {}",
                        selected_demo.get() + 1,
                        scroll_callback_info.get(),
                        error_info.get()
                    )} />
                    <Text content="🚀 Professional ScrollArea with Context-Based Architecture | Error Handling | Performance Optimizations" />
                    <Text content="🧩 Composable Components | Type Safety | React Patterns | Shadcn UI Design | Modern TUI Development" />
                </Layout>
            </Block>
        </Layout>
    }
}

// Helper functions removed - using inline content for better demonstration
// of the new context-based architecture and professional features

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let demo_props = ScrollDemoProps {
        title: "🔄 ScrollArea Component Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <ScrollDemo title={demo_props.title} />
    };

    render(element)
}
