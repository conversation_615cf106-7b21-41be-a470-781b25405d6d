use proc_macro::TokenStream;

mod codegen;
mod errors;
mod parsing;
mod validation;

use codegen::RsxCodeGenerator;
use errors::RsxMacroError;
use parsing::RsxElement;
use validation::RsxValidate;

/// The main RSX macro for creating virtual DOM elements with enhanced validation and analysis
#[proc_macro]
pub fn rsx(input: TokenStream) -> TokenStream {
    // Enhanced parsing with better error handling
    let rsx_element = match syn::parse::<RsxElement>(input) {
        Ok(element) => element,
        Err(parse_error) => {
            // Convert syn::Error to our enhanced RsxMacroError with suggestions
            let enhanced_error = RsxMacroError::syntax_error(
                parse_error.span(),
                format!("Failed to parse RSX syntax: {}", parse_error),
            );
            return enhanced_error.to_syn_error().to_compile_error().into();
        }
    };

    // Enhanced validation with actionable error messages
    #[cfg(debug_assertions)]
    if let Err(validation_error) = rsx_element.validate() {
        return validation_error.to_syn_error().to_compile_error().into();
    }

    let output = RsxCodeGenerator::generate(&rsx_element);
    output.into()
}
